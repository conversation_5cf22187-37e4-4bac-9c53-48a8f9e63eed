package qdrant

import (
	"context"
	"testing"
)

// 集成测试（需要本机 Qdrant 6334），仅做最小验证
func TestEnsureUpsertSearch(t *testing.T) {
	c, err := NewClient(context.Background(), QdrantTestConfig())
	if err != nil {
		t.<PERSON>("skip: cannot create client: %v", err)
	}
	defer c.Close()

	ctx := context.Background()
	col := "unit_test_demo"
	if err := c.EnsureCollection(ctx, col, 4, Distance_Cosine); err != nil {
		t.Fatalf("ensure: %v", err)
	}
	if err := c.UpsertPoints(ctx, col, []Point{{ID: 1001, Vector: []float32{1, 0, 0, 0}, Payload: map[string]any{"text": "hello"}}}); err != nil {
		t.Fatalf("upsert: %v", err)
	}
	resp, err := c.<PERSON>TopK(ctx, col, []float32{1, 0, 0, 0}, 1, true)
	if err != nil {
		t.Fatalf("search: %v", err)
	}
	if len(resp.GetPoints()) == 0 {
		t.Fatalf("no results")
	}
}

