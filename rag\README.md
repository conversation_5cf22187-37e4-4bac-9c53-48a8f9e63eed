# RAG (Retrieval-Augmented Generation) in Go

本项目是一个遵循 Go 社区最佳实践（Standard Go Project Layout）的 RAG 系统基础骨架，集成：

- 向量数据库：Qdrant（github.com/qdrant/go-client/qdrant）
- LLM 编排：Eino 框架（github.com/cloudwego/eino）

目录结构（简化版）：

- cmd/rag: 主应用入口
- internal/config: 配置管理（环境变量/默认值）
- internal/vector/qdrant: Qdrant 向量数据库集成
- internal/orchestrator/eino: 使用 Eino 的工作流/编排组件
- internal/rag: RAG 业务逻辑
- internal/..._test.go: 基础测试结构

快速开始：

1. 进入子模块并初始化依赖：
   - go mod tidy（首次由我们已添加 go.mod / go.sum）
2. 编译运行：
   - go run ./cmd/rag

环境变量：
- QDRANT_HOST（默认：localhost）
- QDRANT_PORT（默认：6334）
- QDRANT_API_KEY（可选）
- QDRANT_USE_TLS（默认：false）

后续扩展建议：
- 在 internal/orchestrator/eino 中引入具体 ChatModel（如 OpenAI/自行部署模型），将检索结果与提示词模板接入 Eino Graph/Workflow
- 在 internal/vector/qdrant 中完善 collection 管理、索引参数、过滤检索等
- 在 internal/rag 中补充嵌入生成器（embedding），将文本入库与检索串接

