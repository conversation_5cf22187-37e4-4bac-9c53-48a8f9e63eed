package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/example/rag/internal/config"
	qclient "github.com/example/rag/internal/vector/qdrant"
)

func main() {
	ctx := context.Background()

	cfg := config.Load()
	fmt.Printf("RAG starting... Qdrant: %s:%d TLS=%v\n", cfg.Qdrant.Host, cfg.Qdrant.Port, cfg.Qdrant.UseTLS)

	qc, err := qclient.NewClient(ctx, cfg.Qdrant)
	if err != nil {
		log.Fatalf("failed to init qdrant client: %v", err)
	}
	defer qc.Close()

	// 演示：检查服务器可用性
	ok, err := qc.HealthCheck(ctx)
	if err != nil {
		log.Fatalf("qdrant health check error: %v", err)
	}
	if !ok {
		log.Fatalf("qdrant not healthy")
	}
	fmt.Println("Qdrant healthy. Project skeleton ready.")

	// TODO: 在此处调用 internal/rag 构建的基本 RAG 流程，或 orchestrator/eino 图
	_ = os.Getenv("PLACEHOLDER")
}

