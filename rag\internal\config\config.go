package config

import (
	"os"
	"strconv"
)

type QdrantConfig struct {
	Host   string
	Port   int
	APIKey string
	UseTLS bool
}

type Config struct {
	Qdrant QdrantConfig
}

func Load() Config {
	return Config{
		Qdrant: QdrantConfig{
			Host:   getenv("QDRANT_HOST", "localhost"),
			Port:   getenvInt("QDRANT_PORT", 6334),
			APIKey: getenv("QDRANT_API_KEY", ""),
			UseTLS: getenvBool("QDRANT_USE_TLS", false),
		},
	}
}

func getenv(key, def string) string {
	if v := os.Getenv(key); v != "" {
		return v
	}
	return def
}

func getenvInt(key string, def int) int {
	if v := os.Getenv(key); v != "" {
		if n, err := strconv.Atoi(v); err == nil {
			return n
		}
	}
	return def
}

func getenvBool(key string, def bool) bool {
	if v := os.Getenv(key); v != "" {
		if v == "1" || v == "true" || v == "TRUE" || v == "True" {
			return true
		}
		if v == "0" || v == "false" || v == "FALSE" || v == "False" {
			return false
		}
	}
	return def
}

