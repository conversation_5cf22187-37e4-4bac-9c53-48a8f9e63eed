package rag

import (
	"context"
	"fmt"
	"strings"

	q "wkx/wuye/knowledgebase/internal/vector/qdrant"
)

// SimplePipeline 一个最小可运行的 RAG Pipeline：
// - 使用固定集合与固定维度
// - 直接用传入的 query 作为“向量”（演示用，真实场景应使用 embedding）
// - 检索 topK 并拼接 payload，模拟回答

type SimplePipeline struct {
	Vec   *q.Client
	Col   string
	Dim   uint32
}

func NewSimplePipeline(vec *q.Client, collection string, dim uint32) *SimplePipeline {
	return &SimplePipeline{Vec: vec, Col: collection, Dim: dim}
}

func (p *SimplePipeline) Ask(ctx context.Context, query string) (string, error) {
	if p.Vec == nil {
		return "", fmt.Errorf("vector client is nil")
	}
	// 演示：把 query 的 rune 长度做成一个简单伪向量（非真实 embedding）
	// 实际应调用 embedding 模型得到 Dim 维向量
	v := make([]float32, p.Dim)
	if p.Dim > 0 {
		v[0] = float32(len([]rune(query)))
	}
	// 检索 topK=3
	resp, err := p.Vec.SearchTopK(ctx, p.Col, v, 3, true)
	if err != nil {
		return "", err
	}
	// 拼接结果的 payload 作为“参考资料摘要”
	var parts []string
	for _, r := range resp.GetPoints() {
		// 只取一个常见字段
		if r.GetPayload() != nil && r.GetPayload().Fields["text"] != nil {
			parts = append(parts, r.GetPayload().Fields["text"].GetStringValue())
		}
	}
	if len(parts) == 0 {
		return "未检索到相关内容（演示）", nil
	}
	return "基于相似内容：" + strings.Join(parts, " | ") + "\n答案（演示）：" + query, nil
}

