package main

import (
	"context"
	"fmt"
	"log"

	"wkx/wuye/knowledgebase/internal/config"
	ragpkg "wkx/wuye/knowledgebase/internal/rag"
	qclient "wkx/wuye/knowledgebase/internal/vector/qdrant"
)

func main() {
	ctx := context.Background()

	cfg := config.Load()
	// 确认使用本机 gRPC 端口 6334，可通过环境变量覆盖
	if cfg.Qdrant.Host == "" { cfg.Qdrant.Host = "localhost" }
	if cfg.Qdrant.Port == 0 { cfg.Qdrant.Port = 6334 }

	fmt.Printf("RAG starting... Qdrant: %s:%d TLS=%v\n", cfg.Qdrant.Host, cfg.Qdrant.Port, cfg.Qdrant.UseTLS)

	qc, err := qclient.NewClient(ctx, cfg.Qdrant)
	if err != nil {
		log.Fatalf("failed to init qdrant client: %v", err)
	}
	defer qc.Close()

	// 健康检查
	ok, err := qc.HealthCheck(ctx)
	if err != nil || !ok {
		log.Fatalf("qdrant health check failed: %v", err)
	}
	fmt.Println("Qdrant healthy.")

	// 演示：确保集合、写入伪数据、检索 + 简单 RAG
	const (
		collection = "demo_rag"
		dim        = 4
	)
	if err := qc.EnsureCollection(ctx, collection, dim, qclient.Distance_Cosine); err != nil {
		log.Fatalf("ensure collection: %v", err)
	}
	// 插入少量向量（payload 包含 text）
	_ = qc.UpsertPoints(ctx, collection, []qclient.Point{
		{ID: 1, Vector: []float32{1, 0, 0, 0}, Payload: map[string]any{"text": "Go 入门与最佳实践"}},
		{ID: 2, Vector: []float32{2, 0, 0, 0}, Payload: map[string]any{"text": "Qdrant 使用与集合管理"}},
		{ID: 3, Vector: []float32{3, 0, 0, 0}, Payload: map[string]any{"text": "Eino 编排 Graph/Workflow"}},
	})

	pipeline := ragpkg.NewSimplePipeline(qc, collection, dim)
	ans, err := pipeline.Ask(ctx, "如何在 Go 中使用 Qdrant 和 Eino 构建 RAG？")
	if err != nil {
		log.Fatalf("pipeline ask: %v", err)
	}
	fmt.Println("Pipeline Output:\n", ans)
}

