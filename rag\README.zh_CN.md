# RAG 系统（Go 版）基础骨架

本目录提供一个以 Go 实现的 RAG（检索增强生成）系统基础工程结构，遵循 Go 社区约定（Standard Go Project Layout），并集成：

- 向量数据库：Qdrant（github.com/qdrant/go-client）
- LLM 编排框架：Eino（github.com/cloudwego/eino）

目录结构：

- cmd/rag: 主程序入口
- internal/config: 配置加载（环境变量）
- internal/vector/qdrant: Qdrant 客户端封装
- internal/orchestrator/eino: 使用 Eino 的编排组件
- internal/rag: RAG 业务接口/流程定义
- internal/..._test.go: 测试骨架

快速开始：

1. 安装依赖
   - 在 rag 子目录执行：go mod tidy
2. 运行
   - go run ./cmd/rag

环境变量：
- QDRANT_HOST（默认：localhost）
- QDRANT_PORT（默认：6334）
- QDRANT_API_KEY（可选）
- QDRANT_USE_TLS（默认：false）

后续开发建议：
- 添加 Embedding 生成器与入库逻辑（Upsert/UpdateBatch）
- 增加检索 API（Query/Search/Scroll），并将结果注入 Prompt 模板
- 使用 Eino 的 Graph/Workflow 将 Retriever、Prompt、LLM、Tools 组合成可维护的流程

